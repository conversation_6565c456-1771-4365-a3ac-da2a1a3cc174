{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"dayjs": "^1.11.13", "dayjs-nuxt": "^2.1.11", "nuxt": "^3.17.6", "officialblock": "^1.0.5", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"arco-design-nuxt-module": "^0.2.1", "sass": "^1.89.2", "sass-loader": "^16.0.5", "vite-plugin-devtools-json": "^0.3.0"}}