# Nuxt3 SSR性能优化

本项目实现了多种SSR（服务器端渲染）性能优化策略，以提高应用的加载速度、响应性和用户体验。

## 优化策略概述

### 1. 页面级缓存

通过在服务器端缓存完整的页面渲染结果，减少重复渲染的开销。

**实现文件：**
- `server/utils/ssr-optimization.js` - 缓存配置和工具函数
- `server/middleware/ssr-optimization.js` - 缓存中间件实现
- `server/plugins/ssr-cache.js` - 响应钩子和缓存管理

**配置：**
- 缓存生存时间（TTL）：默认60秒
- 缓存键生成：基于URL和用户代理（区分移动端和桌面端）
- 缓存策略：可配置需要缓存和排除缓存的路由

### 2. 组件级缓存

通过缓存组件的渲染结果，减少重复渲染的开销，特别适用于数据不经常变化的组件。

**实现文件：**
- `plugins/directives/cache-component.js` - 组件缓存指令
- `components/examples/CachedComponent.vue` - 缓存组件示例

**使用方法：**
```vue
<template>
  <div v-cache-component="cacheConfig">
    <!-- 组件内容 -->
  </div>
</template>

<script setup>
const cacheConfig = {
  id: 'unique-component-id',  // 组件唯一标识
  ttl: 60000,                 // 缓存生存时间（毫秒）
  deps: [prop1, prop2]        // 缓存依赖项，当这些值变化时重新渲染
};
</script>
```

### 3. 资源预加载

预测用户可能需要的资源并提前加载，减少页面切换时的加载时间。

**实现文件：**
- `plugins/ssr-client-optimization.js` - 资源预加载实现

**使用方法：**
```js
// 在组件或页面中使用
const { $preloadResource } = useNuxtApp();

// 预加载资源
$preloadResource('/api/users', 'fetch');
$preloadResource('/images/banner.jpg', 'image');
$preloadResource('/css/styles.css', 'style');
$preloadResource('/js/vendor.js', 'script');
```

### 4. 静态资源优化

优化静态资源的加载、缓存和压缩策略。

**实现文件：**
- `nuxt.config.ts` - 静态资源配置

**配置：**
- 启用Gzip和Brotli压缩
- 设置长期缓存策略
- 预渲染静态页面

### 5. 性能监控

监控应用性能指标，帮助识别和解决性能瓶颈。

**实现文件：**
- `plugins/performance.js` - 性能监控实现
- `pages/ssr-demo/index.vue` - 性能指标展示

## 演示页面

访问 `/ssr-demo` 路由可以查看SSR性能优化的演示和效果。

## 配置选项

在 `nuxt.config.ts` 中可以配置以下性能优化选项：

```js
export default defineNuxtConfig({
  // ... 其他配置
  
  // SSR性能优化配置
  nitro: {
    // 启用压缩
    compressPublicAssets: {
      gzip: true,
      brotli: true
    },
    // 静态资源缓存策略
    routeRules: {
      // 静态资源缓存
      '/assets/**': {
        headers: {
          'cache-control': 'public, max-age=31536000, immutable'
        }
      },
      // API路由缓存
      '/api/todos': { swr: 30 },
      '/api/users': { swr: 60 },
      // 页面缓存
      '/': { swr: 60 },
      '/demo': { swr: 60 }
    },
    // 预渲染静态页面
    prerender: {
      routes: ['/', '/demo']
    }
  },
  
  // 运行时配置
  runtimeConfig: {
    // 公共配置（客户端可访问）
    public: {
      // 是否启用性能监控
      enablePerformanceMonitoring: true,
      // 是否启用组件级缓存
      enableComponentCaching: true
    },
    // 私有配置（仅服务器可访问）
    ssrCacheEnabled: process.env.NODE_ENV === 'production',
    ssrCacheTtl: 60 // 缓存生存时间（秒）
  }
});
```

## 最佳实践

1. **选择性缓存**：不是所有页面和组件都适合缓存，应根据数据变化频率和用户个性化程度来决定。

2. **缓存失效策略**：实现合理的缓存失效策略，确保用户看到的是最新数据。

3. **监控性能指标**：定期监控关键性能指标，如首次内容绘制（FCP）、最大内容绘制（LCP）等。

4. **渐进式增强**：确保在禁用JavaScript的环境中应用仍能基本工作。

5. **代码分割**：使用动态导入和异步组件减小初始加载包的大小。

## 性能测试

使用以下工具测试SSR性能：

- [Lighthouse](https://developers.google.com/web/tools/lighthouse) - 综合性能评分
- [WebPageTest](https://www.webpagetest.org/) - 详细加载性能分析
- [Chrome DevTools Performance](https://developers.google.com/web/tools/chrome-devtools/performance) - 运行时性能分析

## 注意事项

- 页面缓存可能导致用户看到过时的内容，特别是对于频繁更新的数据。
- 组件缓存可能导致交互性问题，如果缓存的组件包含事件监听器。
- 过度预加载可能浪费带宽和资源，应谨慎选择预加载的资源。