// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  modules: ['arco-design-nuxt-module', 'dayjs-nuxt'],
  
  // 插件配置
  plugins: [
    '~/plugins/official-block.client.js'
  ],
  css: [
    '@/assets/css/variables.css',
    'officialblock/style.css'
  ],
  build: {
  },
  ssr: true,

  // SSR性能优化配置
  nitro: {
    // 启用压缩
    compressPublicAssets: {
      gzip: true,
      brotli: true
    },
    
  
   
  },

  

  
  // 应用级配置
  app: {
    // 页面过渡效果
    pageTransition: { name: 'page', mode: 'out-in' },
 
  },
  

   vite: {
    css: {
      preprocessorOptions: {
        scss: {
          // additionalData: '@use "@/assets/scss/_vars.scss" as *;',
        },
      },
    },
    plugins: [
      require('vite-plugin-devtools-json').default()
    ],
    // 优化外部依赖
    optimizeDeps: {
      include: ['officialblock']
    },
    // 构建配置
    build: {
      rollupOptions: {
        external: [],
        output: {
          globals: {}
        }
      }
    },
    // 修复Vue依赖问题
    define: {
      global: 'globalThis',
    }
  },
})